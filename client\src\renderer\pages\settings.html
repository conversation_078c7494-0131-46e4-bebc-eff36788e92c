<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="settings.title">AI重器 - 设置</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net;">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/styles/themes.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        .settings-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .settings-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            overflow: hidden;
        }

        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .settings-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 50px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-50%) translateY(-2px);
        }

        .settings-content {
            padding: 40px;
        }

        .setting-section {
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .setting-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            flex: 1;
        }

        .setting-label h5 {
            margin: 0;
            color: #333;
            font-weight: 500;
        }

        .setting-label p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 0.9em;
        }

        .setting-control {
            min-width: 200px;
            text-align: right;
        }

        .form-select, .form-control {
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.8);
        }

        .form-select:focus, .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-secondary {
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 500;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .alert {
            border-radius: 15px;
            border: none;
        }

        .version-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        .language-preview {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <div class="settings-card">
            <!-- 设置页面头部 -->
            <div class="settings-header">
                <button type="button" class="btn back-btn" onclick="goBack()">
                    <i class="bi bi-arrow-left me-1"></i><span data-i18n="common.back">返回</span>
                </button>
                <h1><i class="bi bi-gear-fill me-2"></i><span data-i18n="settings.title">设置</span></h1>
            </div>

            <!-- 设置内容 -->
            <div class="settings-content">
                <!-- 用户信息 -->
                <div class="setting-section">
                    <h3>
                        <i class="bi bi-person-circle text-primary"></i>
                        <span data-i18n="settings.user.title">用户信息</span>
                    </h3>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.user.current_user">当前用户</h5>
                            <p id="userDisplayInfo" data-i18n="app.loading">加载中...</p>
                        </div>
                        <div class="setting-control">
                            <button type="button" class="btn btn-outline-danger" onclick="logout()">
                                <i class="bi bi-box-arrow-right me-1"></i>
                                <span data-i18n="main.buttons.logout">退出登录</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 语言设置 -->
                <div class="setting-section">
                    <h3>
                        <i class="bi bi-translate text-primary"></i>
                        <span data-i18n="settings.language.title">语言设置</span>
                    </h3>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.language.interface">界面语言</h5>
                            <p data-i18n="settings.language.description">选择应用程序的显示语言</p>
                        </div>
                        <div class="setting-control">
                            <select class="form-select" id="languageSelect">
                                <option value="zh-CN" data-i18n="settings.language.chinese">中文 (简体)</option>
                                <option value="en-US" data-i18n="settings.language.english">English</option>
                            </select>
                        </div>
                    </div>
                    <div class="language-preview" id="languagePreview">
                        <span data-i18n="settings.language.preview">语言预览：当前选择的语言将立即应用到界面</span>
                    </div>
                </div>

                <!-- 应用设置 -->
                <div class="setting-section">
                    <h3>
                        <i class="bi bi-app text-success"></i>
                        <span data-i18n="settings.app.title">应用设置</span>
                    </h3>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.app.startup">开机自启动</h5>
                            <p data-i18n="settings.app.startup_description">系统启动时自动运行应用</p>
                        </div>
                        <div class="setting-control">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoStartSwitch">
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.app.minimize_to_tray">最小化到系统托盘</h5>
                            <p data-i18n="settings.app.minimize_description">关闭窗口时最小化到系统托盘而不是退出</p>
                        </div>
                        <div class="setting-control">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="minimizeToTraySwitch">
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.app.auto_update">自动更新</h5>
                            <p data-i18n="settings.app.update_description">自动检查并下载应用更新</p>
                        </div>
                        <div class="setting-control">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoUpdateSwitch" checked>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更新设置 -->
                <div class="setting-section">
                    <h3>
                        <i class="bi bi-arrow-repeat text-info"></i>
                        <span data-i18n="settings.update.title">更新设置</span>
                    </h3>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.update.channel">更新通道</h5>
                            <p data-i18n="settings.update.channel_description">选择应用更新的下载源</p>
                        </div>
                        <div class="setting-control">
                            <select class="form-select" id="updateChannelSelect">
                                <option value="github" data-i18n="settings.update.github">GitHub (默认)</option>
                                <option value="gitee" data-i18n="settings.update.gitee">Gitee (中国镜像)</option>
                                <option value="custom" data-i18n="settings.update.custom">自定义</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item" id="customUrlSetting" style="display: none;">
                        <div class="setting-label">
                            <h5 data-i18n="settings.update.custom_url">自定义更新地址</h5>
                            <p data-i18n="settings.update.custom_url_description">输入自定义的更新检查地址</p>
                        </div>
                        <div class="setting-control">
                            <input type="url" class="form-control" id="customUpdateUrl" placeholder="https://example.com/releases">
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.update.check_now">检查更新</h5>
                            <p data-i18n="settings.update.check_description">立即检查是否有可用的应用更新</p>
                        </div>
                        <div class="setting-control">
                            <button type="button" class="btn btn-outline-primary" id="checkUpdateBtn" onclick="checkForUpdates()">
                                <i class="bi bi-arrow-repeat me-1"></i>
                                <span data-i18n="settings.update.check_button">检查更新</span>
                            </button>
                        </div>
                    </div>
                    <div id="updateStatus" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-1"></i>
                            <span id="updateStatusMessage"></span>
                        </div>
                    </div>
                </div>

                <!-- 外观设置 -->
                <div class="setting-section">
                    <h3>
                        <i class="bi bi-palette text-warning"></i>
                        <span data-i18n="settings.appearance.title">外观设置</span>
                    </h3>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.appearance.theme">主题</h5>
                            <p data-i18n="settings.appearance.theme_description">选择应用程序的外观主题</p>
                        </div>
                        <div class="setting-control">
                            <select class="form-select" id="themeSelect">
                                <option value="auto" data-i18n="settings.appearance.auto">跟随系统</option>
                                <option value="light" data-i18n="settings.appearance.light">浅色</option>
                                <option value="dark" data-i18n="settings.appearance.dark">深色</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 关于信息 -->
                <div class="setting-section">
                    <h3>
                        <i class="bi bi-info-circle text-info"></i>
                        <span data-i18n="settings.about.title">关于</span>
                    </h3>
                    <div class="setting-item">
                        <div class="setting-label">
                            <h5 data-i18n="settings.about.version">应用版本</h5>
                            <p data-i18n="settings.about.version_description">当前应用程序版本信息</p>
                        </div>
                        <div class="setting-control">
                            <div class="version-info">
                                <strong id="appVersion">v1.0.4</strong>
                                <br>
                                <small class="text-muted" data-i18n="settings.about.build_date">构建日期: 2024-01-01</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="d-flex gap-3 justify-content-end mt-4">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        <span data-i18n="settings.actions.reset">重置设置</span>
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">
                        <i class="bi bi-check-lg me-1"></i>
                        <span data-i18n="settings.actions.save">保存设置</span>
                    </button>
                </div>

                <!-- 状态提示 -->
                <div id="statusAlert" class="alert alert-success mt-3" style="display: none;">
                    <i class="bi bi-check-circle me-1"></i>
                    <span id="statusMessage"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="settings.js"></script>
</body>
</html>
